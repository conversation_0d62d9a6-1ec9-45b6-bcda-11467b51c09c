#!/usr/bin/env python3
"""
HTML to Letter-Size Format Converter - Main Script

This is the main entry point for the HTML formatting tool that converts
HTML pages to letter-sized format suitable for Excel import and PDF export.

Usage:
    python main.py input.html output.html
    python main.py input_dir/ output_dir/ --directory
    python main.py input.html --excel output.xlsx
    python main.py input.html --full-workflow output_prefix

Author: HTML Formatting Tool
Version: 1.0
"""

import sys
import os
import argparse
import logging
from pathlib import Path

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from html_processor import HTMLProcessor
from excel_integration import ExcelIntegrator

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Main function with enhanced command-line interface."""
    parser = argparse.ArgumentParser(
        description='Convert HTML files to letter-size format for Excel import and PDF export',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic HTML processing
  python main.py input.html output.html

  # Process directory of HTML files
  python main.py input_dir/ output_dir/ --directory

  # Create Excel template from HTML
  python main.py input.html --excel output.xlsx

  # Full workflow: HTML -> cleaned HTML -> Excel template
  python main.py input.html --full-workflow output_prefix

  # With custom configuration and backup
  python main.py input.html output.html --config custom.json --backup --verbose
        """
    )

    parser.add_argument('input', help='Input HTML file or directory')
    parser.add_argument('output', nargs='?', help='Output HTML file or directory')

    # Processing modes
    parser.add_argument('--directory', '-d', action='store_true',
                       help='Process all HTML files in input directory')
    parser.add_argument('--excel', '-e', metavar='EXCEL_FILE',
                       help='Create Excel template from HTML')
    parser.add_argument('--full-workflow', '-f', metavar='OUTPUT_PREFIX',
                       help='Run full workflow: HTML -> cleaned HTML -> Excel template')

    # Configuration options
    parser.add_argument('--config', '-c', default='config/page_settings.json',
                       help='Configuration file path')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--backup', '-b', action='store_true',
                       help='Create backup of original files')

    args = parser.parse_args()

    # Validate arguments
    if not args.excel and not args.full_workflow and not args.output:
        parser.error("Output path required unless using --excel or --full-workflow")

    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Initialize processors
    try:
        html_processor = HTMLProcessor(args.config)
        excel_integrator = ExcelIntegrator(args.config)
    except Exception as e:
        logger.error(f"Failed to initialize processors: {e}")
        return 1

    # Create backup if requested
    if args.backup:
        create_backup(args.input)

    # Execute based on mode
    if args.full_workflow:
        return run_full_workflow(args.input, args.full_workflow, html_processor, excel_integrator)
    elif args.excel:
        return create_excel_only(args.input, args.excel, html_processor, excel_integrator)
    elif args.directory:
        return process_directory(args.input, args.output, html_processor)
    else:
        return process_single_file(args.input, args.output, html_processor)


def run_full_workflow(input_file: str, output_prefix: str, html_processor: HTMLProcessor, excel_integrator: ExcelIntegrator) -> int:
    """Run the complete workflow: HTML -> cleaned HTML -> Excel template."""
    logger.info("Starting full workflow...")

    # Step 1: Process HTML
    cleaned_html = f"{output_prefix}_cleaned.html"
    logger.info(f"Step 1: Processing HTML {input_file} -> {cleaned_html}")

    if not html_processor.process_file(input_file, cleaned_html):
        logger.error("HTML processing failed")
        return 1

    # Step 2: Create Excel template
    excel_file = f"{output_prefix}_template.xlsx"
    logger.info(f"Step 2: Creating Excel template {cleaned_html} -> {excel_file}")

    if not excel_integrator.create_excel_template(cleaned_html, excel_file):
        logger.error("Excel template creation failed")
        return 1

    # Step 3: Generate PDF export instructions
    pdf_file = f"{output_prefix}_final.pdf"
    logger.info(f"Step 3: Generating PDF export instructions for {pdf_file}")

    excel_integrator.export_to_pdf(excel_file, pdf_file)

    # Print summary
    logger.info("Full workflow completed successfully!")
    logger.info(f"Files created:")
    logger.info(f"  - Cleaned HTML: {cleaned_html}")
    logger.info(f"  - Excel template: {excel_file}")
    logger.info(f"  - PDF export instructions: {pdf_file.replace('.pdf', '_export_instructions.txt')}")

    # Print statistics
    stats = html_processor.get_stats()
    logger.info("Processing Statistics:")
    for key, value in stats.items():
        logger.info(f"  {key.replace('_', ' ').title()}: {value}")

    return 0


def create_excel_only(input_file: str, excel_file: str, html_processor: HTMLProcessor, excel_integrator: ExcelIntegrator) -> int:
    """Create Excel template directly from HTML."""
    logger.info(f"Creating Excel template: {input_file} -> {excel_file}")

    # First, process the HTML to clean it up
    temp_html = "temp_cleaned.html"

    if not html_processor.process_file(input_file, temp_html):
        logger.error("HTML processing failed")
        return 1

    # Create Excel template
    if not excel_integrator.create_excel_template(temp_html, excel_file):
        logger.error("Excel template creation failed")
        # Clean up temp file
        try:
            os.remove(temp_html)
        except:
            pass
        return 1

    # Clean up temp file
    try:
        os.remove(temp_html)
    except:
        pass

    logger.info("Excel template created successfully!")
    return 0


def process_directory(input_dir: str, output_dir: str, html_processor: HTMLProcessor) -> int:
    """Process all HTML files in a directory."""
    logger.info(f"Processing directory: {input_dir} -> {output_dir}")

    results = html_processor.process_directory(input_dir, output_dir)

    if 'error' in results:
        logger.error(f"Directory processing failed: {results['error']}")
        return 1

    logger.info(f"Directory processing complete: {results['processed']} files processed, {results['failed']} failed")

    # Print detailed results
    for file_result in results['files']:
        status_symbol = "✓" if file_result['status'] == 'success' else "✗"
        logger.info(f"{status_symbol} {file_result['input']} -> {file_result['output']}")

    # Print statistics
    stats = html_processor.get_stats()
    logger.info("Processing Statistics:")
    for key, value in stats.items():
        logger.info(f"  {key.replace('_', ' ').title()}: {value}")

    return 0


def process_single_file(input_file: str, output_file: str, html_processor: HTMLProcessor) -> int:
    """Process a single HTML file."""
    logger.info(f"Processing file: {input_file} -> {output_file}")

    if not html_processor.process_file(input_file, output_file):
        logger.error("File processing failed")
        return 1

    logger.info("File processing complete")

    # Print statistics
    stats = html_processor.get_stats()
    logger.info("Processing Statistics:")
    for key, value in stats.items():
        logger.info(f"  {key.replace('_', ' ').title()}: {value}")

    return 0


def create_backup(input_path: str) -> None:
    """Create backup of input files."""
    input_path_obj = Path(input_path)

    if input_path_obj.is_file():
        backup_path = input_path_obj.with_suffix(input_path_obj.suffix + '.backup')
        import shutil
        shutil.copy2(input_path, backup_path)
        logger.info(f"Backup created: {backup_path}")

    elif input_path_obj.is_dir():
        backup_dir = input_path_obj.parent / (input_path_obj.name + '_backup')
        import shutil
        shutil.copytree(input_path, backup_dir, dirs_exist_ok=True)
        logger.info(f"Backup directory created: {backup_dir}")


if __name__ == '__main__':
    sys.exit(main())
