#!/usr/bin/env python3
"""
Excel Integration Module

This module provides functionality to facilitate HTML-to-Excel import
and Excel-to-PDF export workflows.

Author: HTML Formatting Tool
Version: 1.0
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from bs4 import BeautifulSoup
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side
from openpyxl.utils import get_column_letter

logger = logging.getLogger(__name__)

class ExcelIntegrator:
    """Class for handling Excel integration workflows."""

    def __init__(self, config_path: str = "config/page_settings.json"):
        """Initialize the Excel integrator."""
        self.config = self._load_config(config_path)

    def _load_config(self, config_path: str) -> Dict:
        """Load configuration from JSON file."""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return self._get_default_config()

    def _get_default_config(self) -> Dict:
        """Return default configuration."""
        return {
            "excel_settings": {
                "page_setup": {
                    "paper_size": "letter",
                    "orientation": "portrait",
                    "margins": {"top": 1, "bottom": 1, "left": 1, "right": 1}
                },
                "font": {"name": "Times New Roman", "size": 11},
                "cell_formatting": {"wrap_text": True, "vertical_alignment": "top"}
            }
        }

    def create_excel_template(self, html_file: str, excel_file: str) -> bool:
        """Create an Excel template from processed HTML."""
        try:
            # Parse the HTML
            with open(html_file, 'r', encoding='utf-8') as f:
                soup = BeautifulSoup(f.read(), 'html.parser')

            # Create workbook
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "Document"

            # Set up page formatting
            self._setup_page_formatting(ws)

            # Extract content and create Excel structure
            self._convert_html_to_excel(soup, ws)

            # Apply formatting
            self._apply_excel_formatting(ws)

            # Save the workbook
            wb.save(excel_file)
            logger.info(f"Excel template created: {excel_file}")
            return True

        except Exception as e:
            logger.error(f"Failed to create Excel template: {e}")
            return False

    def _setup_page_formatting(self, worksheet) -> None:
        """Set up page formatting for letter size with 1-inch margins."""
        # Set page setup
        worksheet.page_setup.paperSize = worksheet.PAPERSIZE_LETTER
        worksheet.page_setup.orientation = worksheet.ORIENTATION_PORTRAIT

        # Set margins (in inches)
        worksheet.page_margins.top = 1
        worksheet.page_margins.bottom = 1
        worksheet.page_margins.left = 1
        worksheet.page_margins.right = 1
        worksheet.page_margins.header = 0.5
        worksheet.page_margins.footer = 0.5

        # Set print options
        worksheet.print_options.horizontalCentered = True
        worksheet.page_setup.fitToWidth = 1
        worksheet.page_setup.fitToHeight = 0  # Allow multiple pages vertically

    def _convert_html_to_excel(self, soup: BeautifulSoup, worksheet) -> None:
        """Convert HTML content to Excel cells."""
        current_row = 1

        # Process body content
        body = soup.find('body')
        if body:
            current_row = self._process_element(body, worksheet, current_row)

        # Auto-adjust column widths
        self._auto_adjust_columns(worksheet)

    def _process_element(self, element, worksheet, current_row: int) -> int:
        """Process an HTML element and add to Excel."""
        if not hasattr(element, 'name') or not element.name:
            return current_row

        if element.name in ['p', 'div', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
            text = element.get_text(strip=True)
            if text:
                # Check if this is editable content
                is_editable = element.get('data-editable') == 'true'

                # Add text to cell
                cell = worksheet.cell(row=current_row, column=1, value=text)

                # Apply formatting based on element type
                if element.name.startswith('h'):
                    cell.font = Font(bold=True, size=14)
                elif is_editable:
                    cell.font = Font(color="0000FF")  # Blue for editable content
                    cell.fill = openpyxl.styles.PatternFill(start_color="FFFF99", end_color="FFFF99", fill_type="solid")

                current_row += 1

            # Don't process children for these elements as we already got the text
            return current_row

        elif element.name == 'table':
            current_row = self._process_table(element, worksheet, current_row)
            return current_row

        elif element.name == 'br':
            current_row += 1
            return current_row

        # Process child elements for other elements
        for child in element.children:
            if hasattr(child, 'name') and child.name:
                current_row = self._process_element(child, worksheet, current_row)

        return current_row

    def _process_table(self, table_element, worksheet, start_row: int) -> int:
        """Process HTML table and convert to Excel."""
        current_row = start_row

        # Process table rows
        for tr in table_element.find_all('tr'):
            current_col = 1

            for td in tr.find_all(['td', 'th']):
                text = td.get_text(strip=True)
                cell = worksheet.cell(row=current_row, column=current_col, value=text)

                # Apply table formatting
                if td.name == 'th':
                    cell.font = Font(bold=True)

                # Add borders
                thin_border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
                cell.border = thin_border

                current_col += 1

            current_row += 1

        return current_row + 1  # Add extra space after table

    def _apply_excel_formatting(self, worksheet) -> None:
        """Apply general formatting to the worksheet."""
        # Set default font
        default_font = Font(name="Times New Roman", size=11)

        # Apply to all cells with content
        for row in worksheet.iter_rows():
            for cell in row:
                if cell.value:
                    if not cell.font.name:  # Only if font not already set
                        cell.font = default_font
                    cell.alignment = Alignment(wrap_text=True, vertical='top')

    def _auto_adjust_columns(self, worksheet) -> None:
        """Auto-adjust column widths based on content."""
        for column in worksheet.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)

            for cell in column:
                if cell.value:
                    max_length = max(max_length, len(str(cell.value)))

            # Set width with some padding, but cap at reasonable maximum
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width

    def export_to_pdf(self, excel_file: str, pdf_file: str) -> bool:
        """Export Excel file to PDF (requires Excel application)."""
        try:
            # This would require Excel application or a library like xlwings
            # For now, provide instructions for manual export
            instructions = f"""
To export {excel_file} to PDF:

1. Open {excel_file} in Microsoft Excel
2. Go to File > Export > Create PDF/XPS
3. Choose the location and name: {pdf_file}
4. Ensure page setup is set to:
   - Paper size: Letter (8.5" x 11")
   - Orientation: Portrait
   - Margins: 1 inch on all sides
5. Click 'Publish'

The document will maintain the letter-size format with proper margins.
"""

            # Save instructions to a text file
            instructions_file = pdf_file.replace('.pdf', '_export_instructions.txt')
            with open(instructions_file, 'w', encoding='utf-8') as f:
                f.write(instructions)

            logger.info(f"Export instructions saved to: {instructions_file}")
            return True

        except Exception as e:
            logger.error(f"Failed to export to PDF: {e}")
            return False
