#!/usr/bin/env python3
"""
HTML to Letter-Size Format Converter

This script converts HTML pages to letter-sized format (8.5" x 11") with 1-inch margins,
suitable for Excel import and PDF export workflow.

Author: HTML Formatting Tool
Version: 1.0
"""

import os
import sys
import json
import re
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from bs4 import BeautifulSoup, Comment
import cssutils
from urllib.parse import urljoin, urlparse

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HTMLProcessor:
    """Main class for processing HTML files to letter-size format."""

    def __init__(self, config_path: str = "config/page_settings.json"):
        """Initialize the HTML processor with configuration."""
        self.config = self._load_config(config_path)
        self.stats = {
            'files_processed': 0,
            'mso_elements_removed': 0,
            'vml_elements_removed': 0,
            'conditional_comments_removed': 0,
            'inline_styles_consolidated': 0
        }

    def _load_config(self, config_path: str) -> Dict:
        """Load configuration from JSON file."""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Configuration file not found: {config_path}")
            return self._get_default_config()
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in configuration file: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict:
        """Return default configuration if config file is not available."""
        return {
            "page_format": {
                "size": {"width": "8.5in", "height": "11in"},
                "margins": {"top": "1in", "right": "1in", "bottom": "1in", "left": "1in"},
                "content_area": {"width": "6.5in", "height": "9in"}
            },
            "fonts": {
                "primary": {"family": "Times New Roman, serif", "size": "11pt"},
                "secondary": {"family": "Arial, sans-serif", "size": "10pt"}
            },
            "cleanup_rules": {
                "remove_mso_elements": True,
                "remove_vml_graphics": True,
                "remove_conditional_comments": True,
                "remove_office_namespaces": True,
                "consolidate_inline_styles": True,
                "standardize_fonts": True
            }
        }

    def process_file(self, input_path: str, output_path: str) -> bool:
        """Process a single HTML file."""
        try:
            logger.info(f"Processing file: {input_path}")

            # Read the HTML file
            with open(input_path, 'r', encoding='utf-8', errors='ignore') as f:
                html_content = f.read()

            # Parse with BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')

            # Step 1: Analyze current structure
            self._analyze_structure(soup)

            # Step 2: Remove Microsoft Office artifacts
            if self.config['cleanup_rules']['remove_office_namespaces']:
                self._remove_office_namespaces(soup)

            if self.config['cleanup_rules']['remove_conditional_comments']:
                self._remove_conditional_comments(html_content)

            if self.config['cleanup_rules']['remove_vml_graphics']:
                self._remove_vml_elements(soup)

            if self.config['cleanup_rules']['remove_mso_elements']:
                self._remove_mso_elements(soup)

            # Step 3: Apply letter-size formatting
            self._apply_letter_size_formatting(soup)

            # Step 4: Consolidate and clean CSS
            if self.config['cleanup_rules']['consolidate_inline_styles']:
                self._consolidate_styles(soup)

            # Step 5: Standardize fonts
            if self.config['cleanup_rules']['standardize_fonts']:
                self._standardize_fonts(soup)

            # Step 6: Optimize for Excel compatibility
            self._optimize_for_excel(soup)

            # Write the processed HTML
            self._write_output(soup, output_path)

            self.stats['files_processed'] += 1
            logger.info(f"Successfully processed: {input_path} -> {output_path}")
            return True

        except Exception as e:
            logger.error(f"Error processing file {input_path}: {str(e)}")
            return False

    def _analyze_structure(self, soup: BeautifulSoup) -> Dict:
        """Analyze the HTML structure and identify key elements."""
        analysis = {
            'has_mso_elements': bool(soup.find_all(attrs={'class': re.compile(r'Mso')})),
            'has_vml_elements': bool(soup.find_all(['v:shape', 'v:shapetype', 'v:group'])),
            'has_office_namespaces': 'xmlns:o' in str(soup) or 'xmlns:w' in str(soup),
            'page_sections': len(soup.find_all(attrs={'class': re.compile(r'WordSection')})),
            'images': len(soup.find_all('img')),
            'tables': len(soup.find_all('table')),
            'inline_styles': len(soup.find_all(attrs={'style': True}))
        }

        logger.info(f"Document analysis: {analysis}")
        return analysis

    def _remove_office_namespaces(self, soup: BeautifulSoup) -> None:
        """Remove Microsoft Office XML namespaces from HTML tag."""
        html_tag = soup.find('html')
        if html_tag:
            # Remove Office-specific namespace attributes
            office_namespaces = ['xmlns:v', 'xmlns:o', 'xmlns:w', 'xmlns:dt', 'xmlns:m']
            for ns in office_namespaces:
                if html_tag.has_attr(ns):
                    del html_tag[ns]
                    logger.debug(f"Removed namespace: {ns}")

    def _remove_conditional_comments(self, html_content: str) -> str:
        """Remove conditional comments from HTML content."""
        # Pattern to match conditional comments
        patterns = [
            r'<!--\[if[^>]*\]>.*?<!\[endif\]-->',
            r'<!--\[if[^>]*\]>.*?<!\[endif\]>',
            r'<!\[if[^>]*\]>.*?<!\[endif\]>',
        ]

        cleaned_content = html_content
        removed_count = 0

        for pattern in patterns:
            matches = re.findall(pattern, cleaned_content, re.DOTALL | re.IGNORECASE)
            removed_count += len(matches)
            cleaned_content = re.sub(pattern, '', cleaned_content, flags=re.DOTALL | re.IGNORECASE)

        self.stats['conditional_comments_removed'] += removed_count
        logger.debug(f"Removed {removed_count} conditional comments")
        return cleaned_content

    def _remove_vml_elements(self, soup: BeautifulSoup) -> None:
        """Remove VML (Vector Markup Language) elements."""
        vml_tags = ['v:shapetype', 'v:shape', 'v:stroke', 'v:formulas', 'v:f', 'v:path',
                   'v:imagedata', 'v:group', 'v:lock', 'v:textbox', 'v:fill']

        removed_count = 0
        for tag_name in vml_tags:
            elements = soup.find_all(tag_name)
            for element in elements:
                element.decompose()
                removed_count += 1

        self.stats['vml_elements_removed'] += removed_count
        logger.debug(f"Removed {removed_count} VML elements")

    def _remove_mso_elements(self, soup: BeautifulSoup) -> None:
        """Remove Microsoft Office specific elements and attributes."""
        removed_count = 0

        # Remove elements with MSO-specific classes
        mso_classes = soup.find_all(attrs={'class': re.compile(r'Mso')})
        for element in mso_classes:
            # Convert to standard HTML elements where possible
            if element.name in ['p', 'span', 'div']:
                element['class'] = [cls for cls in element.get('class', []) if not cls.startswith('Mso')]
                if not element.get('class'):
                    del element['class']
            removed_count += 1

        # Remove MSO-specific attributes from all elements
        for element in soup.find_all():
            if element.attrs:
                mso_attrs = [attr for attr in element.attrs.keys() if attr.startswith('mso-')]
                for attr in mso_attrs:
                    del element[attr]
                    removed_count += 1

        # Remove MSO-specific XML elements
        mso_xml_tags = ['o:DocumentProperties', 'o:CustomDocumentProperties', 'w:WordDocument',
                       'w:LatentStyles', 'w:LsdException', 'm:mathPr', 'o:shapedefaults', 'o:shapelayout']

        for tag_name in mso_xml_tags:
            elements = soup.find_all(tag_name)
            for element in elements:
                element.decompose()
                removed_count += 1

        self.stats['mso_elements_removed'] += removed_count
        logger.debug(f"Removed {removed_count} MSO elements/attributes")

    def _apply_letter_size_formatting(self, soup: BeautifulSoup) -> None:
        """Apply letter-size page formatting with 1-inch margins."""
        # Remove existing @page rules and add new ones
        style_tags = soup.find_all('style')

        for style_tag in style_tags:
            if style_tag.string:
                # Remove existing @page rules
                css_content = re.sub(r'@page[^}]*}', '', style_tag.string, flags=re.DOTALL)
                style_tag.string = css_content

        # Create new letter-size CSS
        letter_size_css = self._generate_letter_size_css()

        # Add new style tag or update existing one
        if style_tags:
            style_tags[0].string = (style_tags[0].string or '') + '\n' + letter_size_css
        else:
            # Create new style tag
            new_style = soup.new_tag('style', type='text/css')
            new_style.string = letter_size_css

            head = soup.find('head')
            if head:
                head.append(new_style)
            else:
                # Create head if it doesn't exist
                head = soup.new_tag('head')
                head.append(new_style)
                soup.html.insert(0, head)

        logger.debug("Applied letter-size formatting")

    def _generate_letter_size_css(self) -> str:
        """Generate CSS for letter-size format with 1-inch margins."""
        config = self.config['page_format']

        css = f"""
/* Letter-size page formatting */
@page {{
    size: {config['size']['width']} {config['size']['height']};
    margin: {config['margins']['top']} {config['margins']['right']} {config['margins']['bottom']} {config['margins']['left']};
}}

@media print {{
    @page {{
        size: {config['size']['width']} {config['size']['height']};
        margin: {config['margins']['top']} {config['margins']['right']} {config['margins']['bottom']} {config['margins']['left']};
    }}

    body {{
        max-width: {config['content_area']['width']};
        margin: 0 auto;
        font-size: 11pt;
        line-height: 1.2;
    }}

    .page-break {{
        page-break-before: always;
    }}

    .no-break {{
        page-break-inside: avoid;
    }}
}}

/* Screen display optimization */
@media screen {{
    body {{
        max-width: {config['content_area']['width']};
        margin: 0 auto;
        padding: {config['margins']['top']};
        background: white;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }}
}}

/* General formatting */
body {{
    font-family: {self.config['fonts']['primary']['family']};
    font-size: {self.config['fonts']['primary']['size']};
    line-height: {self.config['fonts']['primary']['line_height']};
    color: black;
    background: white;
}}
"""
        return css

    def _consolidate_styles(self, soup: BeautifulSoup) -> None:
        """Consolidate inline styles and remove redundant styling."""
        style_count = 0

        # Process all elements with inline styles
        for element in soup.find_all(attrs={'style': True}):
            style_attr = element.get('style', '')

            # Clean up MSO-specific styles
            cleaned_style = self._clean_inline_style(style_attr)

            if cleaned_style:
                element['style'] = cleaned_style
                style_count += 1
            else:
                # Remove empty style attribute
                del element['style']

        self.stats['inline_styles_consolidated'] += style_count
        logger.debug(f"Consolidated {style_count} inline styles")

    def _clean_inline_style(self, style: str) -> str:
        """Clean individual inline style string."""
        if not style:
            return ''

        # Remove MSO-specific properties
        mso_pattern = r'mso-[^:]*:[^;]*;?'
        cleaned = re.sub(mso_pattern, '', style, flags=re.IGNORECASE)

        # Remove empty declarations
        cleaned = re.sub(r';\s*;', ';', cleaned)
        cleaned = re.sub(r'^;+|;+$', '', cleaned)

        # Normalize spacing
        cleaned = re.sub(r'\s*:\s*', ':', cleaned)
        cleaned = re.sub(r'\s*;\s*', '; ', cleaned)

        return cleaned.strip()

    def _standardize_fonts(self, soup: BeautifulSoup) -> None:
        """Standardize font families throughout the document."""
        primary_font = self.config['fonts']['primary']['family']
        secondary_font = self.config['fonts']['secondary']['family']

        # Update font-family in inline styles
        for element in soup.find_all(attrs={'style': True}):
            style = element.get('style', '')
            if 'font-family' in style:
                # Replace with standard fonts
                if 'Times' in style or 'serif' in style:
                    style = re.sub(r'font-family:[^;]*', f'font-family: {primary_font}', style)
                else:
                    style = re.sub(r'font-family:[^;]*', f'font-family: {secondary_font}', style)
                element['style'] = style

        logger.debug("Standardized fonts")

    def _optimize_for_excel(self, soup: BeautifulSoup) -> None:
        """Optimize HTML structure for Excel compatibility."""
        # Convert complex layouts to simple table structures where appropriate
        self._convert_to_tables(soup)

        # Add data attributes for editable content
        self._mark_editable_content(soup)

        # Ensure proper HTML structure
        self._ensure_html_structure(soup)

        logger.debug("Optimized for Excel compatibility")

    def _convert_to_tables(self, soup: BeautifulSoup) -> None:
        """Convert complex div layouts to table structures for Excel."""
        # Find div elements that could be converted to tables
        # This is a simplified implementation - can be expanded based on needs

        # Look for div elements with specific patterns that suggest tabular data
        potential_tables = soup.find_all('div', attrs={'style': re.compile(r'display:\s*table')})

        for div in potential_tables:
            # Convert to actual table if it makes sense
            if self._should_convert_to_table(div):
                table = self._create_table_from_div(soup, div)
                if table:
                    div.replace_with(table)

    def _should_convert_to_table(self, element) -> bool:
        """Determine if an element should be converted to a table."""
        # Simple heuristic - can be improved
        children = element.find_all(['div', 'span', 'p'])
        return len(children) > 2 and any('display:table-cell' in child.get('style', '') for child in children)

    def _create_table_from_div(self, soup: BeautifulSoup, div_element):
        """Create a table element from a div structure."""
        # This is a simplified implementation
        table = soup.new_tag('table')
        table['style'] = 'width: 100%; border-collapse: collapse;'

        # Create a single row for now - can be expanded
        row = soup.new_tag('tr')

        cells = div_element.find_all(['div', 'span'], attrs={'style': re.compile(r'display:\s*table-cell')})
        for cell in cells:
            td = soup.new_tag('td')
            td.string = cell.get_text()
            td['style'] = 'border: 1px solid #ccc; padding: 4px;'
            row.append(td)

        if row.find_all('td'):
            table.append(row)
            return table
        return None

    def _mark_editable_content(self, soup: BeautifulSoup) -> None:
        """Mark content that should be editable in Excel."""
        # Add data attributes to elements that contain variable content
        patterns = [
            r'\$[\d,]+\.?\d*',  # Currency amounts
            r'\d{1,2}/\d{1,2}/\d{4}',  # Dates
            r'\b[A-Z][a-z]+ [A-Z][a-z]+\b',  # Names (simple pattern)
        ]

        for element in soup.find_all(text=True):
            if element.parent and element.parent.name not in ['script', 'style']:
                text = str(element)
                for pattern in patterns:
                    if re.search(pattern, text):
                        if element.parent:
                            element.parent['data-editable'] = 'true'
                            element.parent['class'] = element.parent.get('class', []) + ['editable-content']
                        break

    def _ensure_html_structure(self, soup: BeautifulSoup) -> None:
        """Ensure proper HTML document structure."""
        # Ensure we have html, head, and body tags
        if not soup.find('html'):
            html_tag = soup.new_tag('html')
            html_tag.extend(soup.contents)
            soup.clear()
            soup.append(html_tag)

        html_tag = soup.find('html')
        if not html_tag.find('head'):
            head = soup.new_tag('head')
            html_tag.insert(0, head)

        if not html_tag.find('body'):
            body = soup.new_tag('body')
            # Move all non-head content to body
            for element in html_tag.find_all(recursive=False):
                if element.name != 'head':
                    body.append(element.extract())
            html_tag.append(body)

    def _write_output(self, soup: BeautifulSoup, output_path: str) -> None:
        """Write the processed HTML to output file."""
        # Ensure output directory exists
        output_dir = os.path.dirname(output_path)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

        # Write the HTML with proper formatting
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(soup.prettify())

        logger.debug(f"Output written to: {output_path}")

    def process_directory(self, input_dir: str, output_dir: str) -> Dict:
        """Process all HTML files in a directory."""
        input_path = Path(input_dir)
        output_path = Path(output_dir)

        if not input_path.exists():
            logger.error(f"Input directory does not exist: {input_dir}")
            return {'success': False, 'error': 'Input directory not found'}

        # Create output directory
        output_path.mkdir(parents=True, exist_ok=True)

        # Find all HTML files
        html_files = list(input_path.glob('*.html')) + list(input_path.glob('*.htm'))

        results = {'processed': 0, 'failed': 0, 'files': []}

        for html_file in html_files:
            output_file = output_path / html_file.name

            if self.process_file(str(html_file), str(output_file)):
                results['processed'] += 1
                results['files'].append({'input': str(html_file), 'output': str(output_file), 'status': 'success'})
            else:
                results['failed'] += 1
                results['files'].append({'input': str(html_file), 'output': str(output_file), 'status': 'failed'})

        return results

    def get_stats(self) -> Dict:
        """Get processing statistics."""
        return self.stats.copy()

def main():
    """Main function for command-line interface."""
    parser = argparse.ArgumentParser(
        description='Convert HTML files to letter-size format for Excel import and PDF export',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process a single file
  python html_processor.py input.html output.html

  # Process all HTML files in a directory
  python html_processor.py input_dir/ output_dir/ --directory

  # Use custom configuration
  python html_processor.py input.html output.html --config custom_config.json

  # Enable verbose logging
  python html_processor.py input.html output.html --verbose
        """
    )

    parser.add_argument('input', help='Input HTML file or directory')
    parser.add_argument('output', help='Output HTML file or directory')
    parser.add_argument('--directory', '-d', action='store_true',
                       help='Process all HTML files in input directory')
    parser.add_argument('--config', '-c', default='config/page_settings.json',
                       help='Configuration file path (default: config/page_settings.json)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--backup', '-b', action='store_true',
                       help='Create backup of original files')

    args = parser.parse_args()

    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Initialize processor
    try:
        processor = HTMLProcessor(args.config)
    except Exception as e:
        logger.error(f"Failed to initialize processor: {e}")
        return 1

    # Create backup if requested
    if args.backup:
        create_backup(args.input)

    # Process files
    if args.directory:
        logger.info(f"Processing directory: {args.input} -> {args.output}")
        results = processor.process_directory(args.input, args.output)

        if 'error' in results:
            logger.error(f"Directory processing failed: {results['error']}")
            return 1

        logger.info(f"Directory processing complete: {results['processed']} files processed, {results['failed']} failed")

        # Print detailed results
        for file_result in results['files']:
            status_symbol = "✓" if file_result['status'] == 'success' else "✗"
            logger.info(f"{status_symbol} {file_result['input']} -> {file_result['output']}")

    else:
        logger.info(f"Processing file: {args.input} -> {args.output}")
        success = processor.process_file(args.input, args.output)

        if not success:
            logger.error("File processing failed")
            return 1

        logger.info("File processing complete")

    # Print statistics
    stats = processor.get_stats()
    logger.info("Processing Statistics:")
    for key, value in stats.items():
        logger.info(f"  {key.replace('_', ' ').title()}: {value}")

    return 0


def create_backup(input_path: str) -> None:
    """Create backup of input files."""
    input_path_obj = Path(input_path)

    if input_path_obj.is_file():
        backup_path = input_path_obj.with_suffix(input_path_obj.suffix + '.backup')
        import shutil
        shutil.copy2(input_path, backup_path)
        logger.info(f"Backup created: {backup_path}")

    elif input_path_obj.is_dir():
        backup_dir = input_path_obj.parent / (input_path_obj.name + '_backup')
        import shutil
        shutil.copytree(input_path, backup_dir, dirs_exist_ok=True)
        logger.info(f"Backup directory created: {backup_dir}")


if __name__ == '__main__':
    sys.exit(main())
