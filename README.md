# HTML to Letter-Size Format Converter

A comprehensive tool for converting HTML pages to letter-sized format (8.5" × 11") with 1-inch margins, optimized for Excel import and PDF export workflows.

## Features

- **Universal HTML Processing**: Works with any HTML file, not just Microsoft Word exports
- **Letter-Size Formatting**: Converts to standard 8.5" × 11" page format with 1-inch margins
- **Microsoft Office Cleanup**: Removes VML graphics, MSO elements, and conditional comments
- **Excel Integration**: Creates Excel templates with proper page formatting
- **PDF Export Support**: Provides instructions for maintaining format through Excel-to-PDF export
- **Batch Processing**: Process entire directories of HTML files
- **Configurable**: Customizable settings via JSON configuration
- **Content Marking**: Automatically identifies and marks editable content

## Installation

1. **Clone or download** this repository
2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

### Dependencies

- `beautifulsoup4>=4.12.0` - HTML parsing and manipulation
- `lxml>=4.9.0` - XML/HTML processing
- `openpyxl>=3.1.0` - Excel file creation and manipulation
- `weasyprint>=60.0` - PDF generation (optional)
- `Pillow>=10.0.0` - Image processing
- `cssutils>=2.7.0` - CSS parsing and manipulation
- `html5lib>=1.1` - HTML5 parsing
- `requests>=2.31.0` - HTTP requests (for future features)

## Quick Start

### Basic Usage

```bash
# Process a single HTML file
python main.py input.html output.html

# Process all HTML files in a directory
python main.py input_dir/ output_dir/ --directory

# Create Excel template from HTML
python main.py input.html --excel output.xlsx

# Run complete workflow (HTML → cleaned HTML → Excel template)
python main.py input.html --full-workflow document_name
```

### Example with the provided file

```bash
# Process the CMFA document
python main.py "Web Page - 2C_CMFA Recycling Commitment Letter - 3403 Piedmont Avenue.html" output/cmfa_cleaned.html

# Create Excel template
python main.py "Web Page - 2C_CMFA Recycling Commitment Letter - 3403 Piedmont Avenue.html" --excel output/cmfa_template.xlsx

# Full workflow
python main.py "Web Page - 2C_CMFA Recycling Commitment Letter - 3403 Piedmont Avenue.html" --full-workflow output/cmfa_document
```

## Project Structure

```
HTML Formatting/
├── main.py                 # Main entry point
├── requirements.txt        # Python dependencies
├── README.md              # This file
├── config/
│   └── page_settings.json # Configuration file
├── src/
│   ├── html_processor.py  # Core HTML processing
│   └── excel_integration.py # Excel integration
├── input/                 # Input HTML files
├── output/                # Processed output files
└── temp/                  # Temporary files
```

## Configuration

The tool uses `config/page_settings.json` for configuration:

```json
{
  "page_format": {
    "size": {"width": "8.5in", "height": "11in"},
    "margins": {"top": "1in", "right": "1in", "bottom": "1in", "left": "1in"},
    "content_area": {"width": "6.5in", "height": "9in"}
  },
  "fonts": {
    "primary": {"family": "Times New Roman, serif", "size": "11pt"},
    "secondary": {"family": "Arial, sans-serif", "size": "10pt"}
  },
  "cleanup_rules": {
    "remove_mso_elements": true,
    "remove_vml_graphics": true,
    "remove_conditional_comments": true,
    "remove_office_namespaces": true,
    "consolidate_inline_styles": true,
    "standardize_fonts": true
  }
}
```

## Command Line Options

```
positional arguments:
  input                 Input HTML file or directory
  output                Output HTML file or directory

optional arguments:
  -h, --help            Show help message
  -d, --directory       Process all HTML files in input directory
  -e EXCEL_FILE, --excel EXCEL_FILE
                        Create Excel template from HTML
  -f OUTPUT_PREFIX, --full-workflow OUTPUT_PREFIX
                        Run full workflow: HTML → cleaned HTML → Excel template
  -c CONFIG, --config CONFIG
                        Configuration file path (default: config/page_settings.json)
  -v, --verbose         Enable verbose logging
  -b, --backup          Create backup of original files
```

## Processing Steps

The tool follows a systematic approach:

### Phase 1: HTML Analysis and Cleanup
1. **Structure Analysis**: Identifies document elements, MSO artifacts, and formatting
2. **Microsoft Office Cleanup**: Removes VML graphics, conditional comments, and MSO elements
3. **Namespace Cleanup**: Removes Office-specific XML namespaces
4. **Style Consolidation**: Cleans and consolidates inline styles

### Phase 2: Letter-Size Formatting
1. **Page Setup**: Applies 8.5" × 11" page size with 1-inch margins
2. **CSS Generation**: Creates print-optimized CSS with proper @page rules
3. **Content Constraints**: Ensures content fits within 6.5" × 9" content area
4. **Font Standardization**: Normalizes fonts for consistent rendering

### Phase 3: Excel Optimization
1. **Structure Conversion**: Converts complex layouts to Excel-friendly formats
2. **Content Marking**: Identifies and marks editable content (dates, names, amounts)
3. **Table Optimization**: Ensures proper table structure for Excel import
4. **Metadata Addition**: Adds data attributes for Excel processing

## Excel Integration Workflow

1. **HTML Processing**: Clean and format the HTML file
2. **Excel Template Creation**: Generate Excel file with proper page setup
3. **Content Import**: Transfer HTML content to Excel cells with formatting
4. **Editable Content**: Mark variable content (highlighted in blue/yellow)
5. **PDF Export**: Provide instructions for Excel-to-PDF export

### Excel Features

- **Letter-size page setup** (8.5" × 11")
- **1-inch margins** on all sides
- **Proper font formatting** (Times New Roman, 11pt)
- **Editable content highlighting** (blue text, yellow background)
- **Table structure preservation**
- **Auto-adjusted column widths**

## PDF Export Process

After creating the Excel template:

1. Open the Excel file in Microsoft Excel
2. Go to **File > Export > Create PDF/XPS**
3. Ensure page setup:
   - Paper size: Letter (8.5" × 11")
   - Orientation: Portrait
   - Margins: 1 inch on all sides
4. Click **Publish**

The tool automatically generates detailed export instructions for each file.

## Examples

### Processing Microsoft Word HTML Export

```bash
# Clean up Word-generated HTML
python main.py word_document.html cleaned_document.html --verbose

# Create Excel template with editable content marked
python main.py word_document.html --excel document_template.xlsx

# Full workflow with backup
python main.py word_document.html --full-workflow final_document --backup
```

### Batch Processing

```bash
# Process entire directory
python main.py html_files/ processed_files/ --directory --verbose

# Create Excel templates for all HTML files
for file in html_files/*.html; do
    python main.py "$file" --excel "excel_templates/$(basename "$file" .html).xlsx"
done
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed with `pip install -r requirements.txt`
2. **File Not Found**: Check file paths and ensure input files exist
3. **Permission Errors**: Ensure write permissions for output directories
4. **Memory Issues**: For large files, increase available memory or process in smaller batches

### Verbose Logging

Use the `--verbose` flag to see detailed processing information:

```bash
python main.py input.html output.html --verbose
```

### Configuration Issues

If the configuration file is missing or invalid, the tool will use default settings and log a warning.

## Advanced Usage

### Custom Configuration

Create a custom configuration file:

```json
{
  "page_format": {
    "size": {"width": "8.5in", "height": "11in"},
    "margins": {"top": "0.75in", "right": "0.75in", "bottom": "0.75in", "left": "0.75in"}
  },
  "fonts": {
    "primary": {"family": "Arial, sans-serif", "size": "10pt"}
  }
}
```

Use with:
```bash
python main.py input.html output.html --config custom_config.json
```

### Programmatic Usage

```python
from src.html_processor import HTMLProcessor
from src.excel_integration import ExcelIntegrator

# Initialize processors
html_processor = HTMLProcessor("config/page_settings.json")
excel_integrator = ExcelIntegrator("config/page_settings.json")

# Process HTML
html_processor.process_file("input.html", "output.html")

# Create Excel template
excel_integrator.create_excel_template("output.html", "template.xlsx")

# Get processing statistics
stats = html_processor.get_stats()
print(f"Processed {stats['files_processed']} files")
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is provided as-is for educational and professional use.

## Support

For issues, questions, or feature requests, please create an issue in the repository or contact the development team.
